# Complete uv + pyproject.toml Flow - Step by Step
1. Project Setup: 
    - uv = Modern Python package manager (faster than pip). Speed: 10-100x faster package installation
    - Purpose: Manages packages and virtual environments automatically. More reliable than pip
    - Replaces: pip + venv workflow
2. curl -LsSf https://astral.sh/uv/install.sh | sh
    - curl = download files from the internet
    - L = follow redirects, Follow redirects (if the URL redirects to another URL)
    - s = silent mode (don't show progress bars)
    - S = show errors even in silent mode
    - f = fail silently
    - https://astral.sh/uv/install.sh = url to download
    - | = pipe
    - sh = run the downloaded file
3. uv sync
    - uv sync = install packages and create virtual environment
    - uv sync = pip install -r requirements.txt
4. Configuration File
    - pyproject.toml = Blueprint for the project
    - TOML = "Tom's Obvious Minimal Language" (config file format)
    - Contains: What packages you need, project info, tool settings
5. Dependencies
    - dependencies = [] = Must-have packages to run your code
    - optional-dependencies.dev = [] = Extra packages only for development
    - Example: You need requests to run, but pytest only for testing
4. Virtual Environment

uv sync reads pyproject.toml
Creates: .venv/ folder with all packages installed
Result: Isolated environment for your project

5. Code Quality Tools
mypy = Finds type errors before code runs
ruff = Fixes formatting, finds unused imports, sorts code
Example: Catches add_numbers("5", "10") when expecting integers

6. Package Distribution
setuptools = Tool that packages your code for sharing
wheel (.whl) = Zip file format for Python packages
build_meta = The engine inside setuptools that does the work

7. Package Naming
Format: name-version-python-abi-platform.whl
Example: myproject-1.0.0-py3-none-any.whl
py3 = Python 3, none = no compiled code, any = works everywhere

8. Making Your Code Importable
packages = ["your_project"] = Makes from your_project import X work
package-dir = Points to where your source code lives
py.typed = Tells other tools "this package has type hints"

9. Tool Configuration
[tool.ruff] = Settings for code formatter/linter
lint.select = Which rules to enforce (spacing, imports, docs)
lint.ignore = Which rules to skip

10. Test File Exceptions
"tests/*" = Special rules for test files
Skip docs = Test files don't need documentation
Allow older syntax = Tests can use simpler code

11. The Complete Flow
Write pyproject.toml with dependencies
Run uv sync → creates .venv/ and installs packages
Activate environment → source .venv/bin/activate
Write code in src/your_project/
Run ruff and mypy to clean code
Build package → creates .whl file for distribution

